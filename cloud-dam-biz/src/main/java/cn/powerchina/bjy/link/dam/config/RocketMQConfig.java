package cn.powerchina.bjy.link.dam.config;

import org.apache.rocketmq.client.core.RocketMQClientTemplate;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * RocketMQ 配置类
 *
 * <AUTHOR>
 */
@Configuration
@ConditionalOnProperty(name = "rocketmq.name-server")
public class RocketMQConfig {

    /**
     * 手动创建 RocketMQClientTemplate Bean
     * 当自动配置失败时作为备用方案
     */
    @Bean
    @ConditionalOnMissingBean(RocketMQClientTemplate.class)
    public RocketMQClientTemplate rocketMQClientTemplate() {
        return new RocketMQClientTemplate();
    }
}
